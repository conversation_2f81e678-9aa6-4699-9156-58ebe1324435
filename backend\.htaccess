# Era Timbers Backend Security and Configuration

# Prevent access to sensitive files
<Files ".env">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "composer.json">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "composer.lock">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Prevent directory browsing
Options -Indexes

# Enable CORS for contact.php
<Files "contact.php">
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
    Header always set Access-Control-Max-Age "3600"
</Files>

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Remove server signature
ServerTokens Prod
Header unset Server

# PHP security settings
php_flag display_errors Off
php_flag log_errors On
php_value error_log /path/to/your/error.log
