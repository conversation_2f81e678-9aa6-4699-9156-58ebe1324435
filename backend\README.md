# Era Timbers Contact Backend

PHP backend for handling contact form submissions from the Era Timbers React website.

## Features

- ✅ Secure form validation and sanitization
- ✅ PHPMailer integration for reliable email delivery
- ✅ CORS support for React frontend
- ✅ Environment-based configuration
- ✅ Comprehensive error handling
- ✅ HTML and plain text email formats
- ✅ Input validation with detailed error messages

## Requirements

- PHP 7.4 or higher
- Composer
- SMTP server access (Gmail, hosting provider, etc.)

## Installation

1. **Install dependencies:**
   ```bash
   cd backend
   composer install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   ```

3. **Edit `.env` file with your SMTP settings:**
   ```env
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your_app_password_here
   SMTP_ENCRYPTION=tls
   
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Era Timbers Website
   TO_EMAIL=<EMAIL>
   TO_NAME=Sales Team
   
   ALLOWED_ORIGIN=https://yourdomain.com
   DEBUG_MODE=false
   ```

## SMTP Configuration

### Gmail Setup
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use the generated password in `SMTP_PASSWORD`

### Other Providers
- **Outlook/Hotmail:** `smtp-mail.outlook.com:587`
- **Yahoo:** `smtp.mail.yahoo.com:587`
- **Custom hosting:** Check with your hosting provider

## API Endpoint

**URL:** `POST /backend/contact.php`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "message": "Hello, I'm interested in your products."
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Thank you for your message! We will get back to you soon."
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Validation failed",
  "details": ["Email is required", "Message is too short"]
}
```

## Validation Rules

- **First Name:** Required, max 100 characters
- **Last Name:** Required, max 100 characters
- **Email:** Required, valid email format
- **Phone:** Required, valid phone format (10-20 characters)
- **Message:** Required, 10-2000 characters

## Security Features

- Input sanitization and validation
- CORS protection
- HTML entity encoding
- Error logging
- Rate limiting ready (can be added)

## Deployment

1. Upload the `backend` folder to your web server
2. Ensure PHP and Composer are available
3. Run `composer install --no-dev` on the server
4. Configure your `.env` file with production settings
5. Set appropriate file permissions
6. Test the endpoint

## Testing

You can test the endpoint using curl:

```bash
curl -X POST https://yourdomain.com/backend/contact.php \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "phone": "+**********",
    "message": "This is a test message."
  }'
```

## Troubleshooting

1. **SMTP Authentication Failed:**
   - Check username/password
   - Verify SMTP settings
   - Enable "Less secure app access" or use App Passwords

2. **CORS Errors:**
   - Update `ALLOWED_ORIGIN` in `.env`
   - Check browser console for specific errors

3. **500 Internal Server Error:**
   - Check PHP error logs
   - Verify file permissions
   - Enable `DEBUG_MODE=true` for detailed errors

4. **Composer Issues:**
   - Ensure Composer is installed
   - Run `composer install` in the backend directory

## File Structure

```
backend/
├── composer.json          # Dependencies
├── config.php            # Configuration loader
├── contact.php           # Main endpoint
├── .env.example          # Environment template
├── .env                  # Your environment (create this)
├── README.md             # This file
└── vendor/               # Composer dependencies
```
