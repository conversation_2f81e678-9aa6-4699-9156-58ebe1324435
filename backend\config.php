<?php
/**
 * Configuration file for Era Timbers Contact Backend
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Email Configuration
define('SMTP_HOST', $_ENV['SMTP_HOST'] ?? 'smtp.gmail.com');
define('SMTP_PORT', $_ENV['SMTP_PORT'] ?? 587);
define('SMTP_USERNAME', $_ENV['SMTP_USERNAME'] ?? '');
define('SMTP_PASSWORD', $_ENV['SMTP_PASSWORD'] ?? '');
define('SMTP_ENCRYPTION', $_ENV['SMTP_ENCRYPTION'] ?? 'tls');

// Email Settings
define('FROM_EMAIL', $_ENV['FROM_EMAIL'] ?? '<EMAIL>');
define('FROM_NAME', $_ENV['FROM_NAME'] ?? 'Era Timbers Website');
define('TO_EMAIL', $_ENV['TO_EMAIL'] ?? '<EMAIL>');
define('TO_NAME', $_ENV['TO_NAME'] ?? 'Sales Team');

// CORS Settings
define('ALLOWED_ORIGIN', $_ENV['ALLOWED_ORIGIN'] ?? '*');

// Debug Mode
define('DEBUG_MODE', filter_var($_ENV['DEBUG_MODE'] ?? false, FILTER_VALIDATE_BOOLEAN));

// Validation Rules
define('MAX_NAME_LENGTH', 100);
define('MAX_MESSAGE_LENGTH', 2000);
define('MIN_MESSAGE_LENGTH', 5);
