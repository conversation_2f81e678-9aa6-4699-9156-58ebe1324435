<?php
/**
 * Era Timbers Contact Form Handler
 * Handles form submissions from the React frontend
 */

require_once __DIR__ . '/config.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

// Start output buffering to prevent any unwanted output
ob_start();

// Set response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: ' . ALLOWED_ORIGIN);
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

/**
 * Validate and sanitize input data
 */
function validateInput($data) {
    $errors = [];
    
    // Get and sanitize inputs
    $firstName = trim($data['firstName'] ?? '');
    $lastName = trim($data['lastName'] ?? '');
    $email = trim($data['email'] ?? '');
    $phone = trim($data['phone'] ?? '');
    $message = trim($data['message'] ?? '');
    
    // Validate first name
    if (empty($firstName)) {
        $errors[] = 'First name is required';
    } elseif (strlen($firstName) > MAX_NAME_LENGTH) {
        $errors[] = 'First name is too long';
    }
    
    // Validate last name
    if (empty($lastName)) {
        $errors[] = 'Last name is required';
    } elseif (strlen($lastName) > MAX_NAME_LENGTH) {
        $errors[] = 'Last name is too long';
    }
    
    // Validate email
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    // Validate phone
    if (empty($phone)) {
        $errors[] = 'Phone number is required';
    } elseif (!preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', $phone)) {
        $errors[] = 'Invalid phone number format';
    }
    
    // Validate message
    if (empty($message)) {
        $errors[] = 'Message is required';
    } elseif (strlen($message) < MIN_MESSAGE_LENGTH) {
        $errors[] = 'Message is too short (minimum ' . MIN_MESSAGE_LENGTH . ' characters)';
    } elseif (strlen($message) > MAX_MESSAGE_LENGTH) {
        $errors[] = 'Message is too long (maximum ' . MAX_MESSAGE_LENGTH . ' characters)';
    }
    
    if (!empty($errors)) {
        return ['valid' => false, 'errors' => $errors];
    }
    
    return [
        'valid' => true,
        'data' => [
            'firstName' => htmlspecialchars($firstName, ENT_QUOTES, 'UTF-8'),
            'lastName' => htmlspecialchars($lastName, ENT_QUOTES, 'UTF-8'),
            'email' => filter_var($email, FILTER_SANITIZE_EMAIL),
            'phone' => htmlspecialchars($phone, ENT_QUOTES, 'UTF-8'),
            'message' => htmlspecialchars($message, ENT_QUOTES, 'UTF-8')
        ]
    ];
}

/**
 * Send email using PHPMailer
 */
function sendEmail($data) {
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        // Disable all debug output to ensure clean JSON response
        $mail->SMTPDebug = 0;
        
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        
        // Recipients
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress(TO_EMAIL, TO_NAME);
        $mail->addReplyTo($data['email'], $data['firstName'] . ' ' . $data['lastName']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'New Contact Form Submission - Era Timbers';
        
        // Create email body
        $emailBody = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
                .content { background-color: #ffffff; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; }
                .field { margin-bottom: 15px; }
                .label { font-weight: bold; color: #495057; }
                .value { margin-top: 5px; }
                .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2 style='margin: 0; color: #ff6b35;'>New Contact Form Submission</h2>
                    <p style='margin: 5px 0 0 0;'>Era Timbers Website</p>
                </div>
                
                <div class='content'>
                    <div class='field'>
                        <div class='label'>Name:</div>
                        <div class='value'>{$data['firstName']} {$data['lastName']}</div>
                    </div>
                    
                    <div class='field'>
                        <div class='label'>Email:</div>
                        <div class='value'>{$data['email']}</div>
                    </div>
                    
                    <div class='field'>
                        <div class='label'>Phone:</div>
                        <div class='value'>{$data['phone']}</div>
                    </div>
                    
                    <div class='field'>
                        <div class='label'>Message:</div>
                        <div class='value'>" . nl2br($data['message']) . "</div>
                    </div>
                </div>
                
                <div class='footer'>
                    <p>This email was sent from the Era Timbers contact form on " . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </body>
        </html>";
        
        $mail->Body = $emailBody;
        
        // Plain text version
        $mail->AltBody = "New Contact Form Submission - Era Timbers\n\n" .
                        "Name: {$data['firstName']} {$data['lastName']}\n" .
                        "Email: {$data['email']}\n" .
                        "Phone: {$data['phone']}\n" .
                        "Message:\n{$data['message']}\n\n" .
                        "Sent on: " . date('Y-m-d H:i:s');
        
        $mail->send();
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("PHPMailer Error: " . $mail->ErrorInfo);
        return ['success' => false, 'error' => DEBUG_MODE ? $mail->ErrorInfo : 'Failed to send email'];
    }
}

// Main execution
try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate input
    $validation = validateInput($data);
    
    if (!$validation['valid']) {
        ob_clean();
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Validation failed',
            'details' => $validation['errors']
        ]);
        exit();
    }
    
    // Send email
    $emailResult = sendEmail($validation['data']);
    
    if ($emailResult['success']) {
        // Clean any output buffer before sending JSON
        ob_clean();
        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your message! We will get back to you soon.'
        ]);
    } else {
        // Clean any output buffer before sending JSON
        ob_clean();
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $emailResult['error']
        ]);
    }
    
} catch (Exception $e) {
    error_log("Contact Form Error: " . $e->getMessage());
    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => DEBUG_MODE ? $e->getMessage() : 'An error occurred while processing your request'
    ]);
}
