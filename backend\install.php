<?php
/**
 * Era Timbers Backend Installation Script
 * Run this script once to set up the backend
 */

echo "<h1>Era Timbers Backend Installation</h1>";

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    echo "<p style='color: red;'>❌ PHP 7.4 or higher is required. Current version: " . PHP_VERSION . "</p>";
    exit;
}
echo "<p style='color: green;'>✅ PHP version: " . PHP_VERSION . "</p>";

// Check if Composer is available
if (!file_exists(__DIR__ . '/vendor/autoload.php')) {
    echo "<p style='color: red;'>❌ Composer dependencies not installed. Run 'composer install' first.</p>";
    exit;
}
echo "<p style='color: green;'>✅ Composer dependencies installed</p>";

// Check if .env file exists
if (!file_exists(__DIR__ . '/.env')) {
    if (file_exists(__DIR__ . '/.env.example')) {
        copy(__DIR__ . '/.env.example', __DIR__ . '/.env');
        echo "<p style='color: orange;'>⚠️ Created .env file from .env.example. Please configure your SMTP settings.</p>";
    } else {
        echo "<p style='color: red;'>❌ .env.example file not found</p>";
        exit;
    }
} else {
    echo "<p style='color: green;'>✅ .env file exists</p>";
}

// Load environment variables
require_once __DIR__ . '/config.php';

// Test SMTP configuration
echo "<h2>SMTP Configuration Test</h2>";

if (empty(SMTP_USERNAME) || empty(SMTP_PASSWORD)) {
    echo "<p style='color: orange;'>⚠️ SMTP credentials not configured. Please update your .env file.</p>";
} else {
    echo "<p style='color: green;'>✅ SMTP credentials configured</p>";
    
    // Test SMTP connection (basic check)
    try {
        $socket = @fsockopen(SMTP_HOST, SMTP_PORT, $errno, $errstr, 10);
        if ($socket) {
            echo "<p style='color: green;'>✅ SMTP server reachable: " . SMTP_HOST . ":" . SMTP_PORT . "</p>";
            fclose($socket);
        } else {
            echo "<p style='color: red;'>❌ Cannot reach SMTP server: " . SMTP_HOST . ":" . SMTP_PORT . " ($errstr)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ SMTP connection test failed: " . $e->getMessage() . "</p>";
    }
}

// Check file permissions
echo "<h2>File Permissions</h2>";

$files_to_check = [
    'contact.php' => 'readable',
    'config.php' => 'readable',
    '.env' => 'readable'
];

foreach ($files_to_check as $file => $permission) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        if (is_readable($filepath)) {
            echo "<p style='color: green;'>✅ $file is readable</p>";
        } else {
            echo "<p style='color: red;'>❌ $file is not readable</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ $file does not exist</p>";
    }
}

// Test endpoint
echo "<h2>Endpoint Test</h2>";

$test_data = [
    'firstName' => 'Test',
    'lastName' => 'User',
    'email' => '<EMAIL>',
    'phone' => '+1234567890',
    'message' => 'This is a test message from the installation script.'
];

echo "<p>Testing contact.php endpoint with sample data...</p>";

// Simulate a POST request to contact.php
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// Capture output
ob_start();
$old_input = file_get_contents('php://input');

// Mock the input
$GLOBALS['HTTP_RAW_POST_DATA'] = json_encode($test_data);

// Include the contact script (this is a simplified test)
try {
    // We can't easily test the full contact.php without mocking php://input
    echo "<p style='color: orange;'>⚠️ Manual testing required. Use the test curl command in README.md</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing endpoint: " . $e->getMessage() . "</p>";
}

ob_end_clean();

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Configure your SMTP settings in the .env file</li>";
echo "<li>Test the endpoint using the curl command in README.md</li>";
echo "<li>Update your React app's backend URL</li>";
echo "<li>Deploy and test the complete integration</li>";
echo "</ol>";

echo "<h2>Environment Variables to Configure</h2>";
echo "<pre>";
echo "SMTP_HOST=your_smtp_host\n";
echo "SMTP_USERNAME=<EMAIL>\n";
echo "SMTP_PASSWORD=your_app_password\n";
echo "ALLOWED_ORIGIN=https://yourdomain.com\n";
echo "</pre>";

echo "<p><strong>Installation complete!</strong> Remember to delete this install.php file after setup.</p>";
?>
