import { Award, Users, Globe, Shield } from 'lucide-react'

const About = () => {
  const features = [
    {
      icon: Award,
      title: 'ISO 9001 Certified',
      description: 'Quality management systems ensuring excellence in every project'
    },
    {
      icon: Users,
      title: 'Expert Team',
      description: 'Qualified engineers, technicians, and skilled labor force'
    },
    {
      icon: Globe,
      title: 'Strategic Locations',
      description: 'Offices in Jubail, Taif, and Bahrain for regional coverage'
    },
    {
      icon: Shield,
      title: 'Safety First',
      description: 'Comprehensive safety programs and environmental protection'
    }
  ]

  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div>
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-6">
              About Elite Structure Company
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Building Excellence Since 2008
            </h2>
            
            <p className="text-lg text-gray-600 mb-6">
              Elite Structure Company (formerly MRB Contracting Co Ltd.) is an ISO 9001 Certified Company 
              with proven expertise in the Saudi market. We are a premier sub-contractor for prestigious 
              companies including SAUDI ARAMCO, SABIC, SAUDI ELECTRICITY COMPANY, and Maaden.
            </p>
            
            <p className="text-gray-600 mb-8">
              Our specialized expertise lies in non-metallic piping (GRP, RTR and HDPE), construction, 
              contracting, and maintenance services. We handle projects ranging from short-term to 
              long-term multi-discipline operations across various industries.
            </p>

            {/* Features Grid */}
            <div className="grid sm:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <feature.icon className="w-5 h-5 text-primary" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{feature.title}</h3>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Stats and Achievements */}
          <div className="bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Our Achievements</h3>
            
            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
                <div>
                  <div className="text-2xl font-bold text-primary">15+</div>
                  <div className="text-sm text-gray-600">Years of Excellence</div>
                </div>
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <Award className="w-6 h-6 text-primary" />
                </div>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
                <div>
                  <div className="text-2xl font-bold text-primary">500+</div>
                  <div className="text-sm text-gray-600">Projects Completed</div>
                </div>
                <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center">
                  <Globe className="w-6 h-6 text-accent-foreground" />
                </div>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
                <div>
                  <div className="text-2xl font-bold text-primary">3</div>
                  <div className="text-sm text-gray-600">Strategic Offices</div>
                </div>
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <Users className="w-6 h-6 text-primary" />
                </div>
              </div>
            </div>

            <div className="mt-8 p-6 bg-white rounded-lg border-l-4 border-primary">
              <h4 className="font-semibold text-gray-900 mb-2">Our Mission</h4>
              <p className="text-gray-600 text-sm">
                To provide exceptional construction and engineering services that conform to specific 
                client requirements and regulatory standards, ensuring quality, safety, and environmental protection.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About

