import { Star, Award, Handshake, TrendingUp } from 'lucide-react'

const Clients = () => {
  const clients = [
    {
      name: 'SAUDI ARAMCO',
      description: 'Saudi Arabia\'s national petroleum and natural gas company',
      partnership: 'Premier Sub-Contractor',
      projects: '200+'
    },
    {
      name: 'SABIC',
      description: 'Saudi Basic Industries Corporation and affiliate companies',
      partnership: 'Trusted Partner',
      projects: '150+'
    },
    {
      name: 'SAUDI ELECTRICITY COMPANY',
      description: 'The national electric utility company',
      partnership: 'Certified Contractor',
      projects: '100+'
    },
    {
      name: 'MAADEN',
      description: 'Saudi Arabian Mining Company',
      partnership: 'Specialized Services',
      projects: '50+'
    }
  ]

  const testimonials = [
    {
      quote: "Elite Structure Company has consistently delivered exceptional quality in our projects. Their expertise in non-metallic piping is unmatched.",
      author: "Project Manager",
      company: "Major Oil & Gas Company",
      rating: 5
    },
    {
      quote: "Their commitment to safety and environmental standards aligns perfectly with our corporate values. Highly recommended.",
      author: "Operations Director",
      company: "Petrochemical Facility",
      rating: 5
    }
  ]

  return (
    <section id="clients" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-6">
            Our Clients
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Trusted by Industry Leaders
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We are proud to serve as a premier sub-contractor for some of the most prestigious 
            companies in the region, building long-term partnerships based on trust and excellence.
          </p>
        </div>

        {/* Client Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-16">
          <div className="text-center p-6 bg-white rounded-xl shadow-sm">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Handshake className="w-6 h-6 text-primary" />
            </div>
            <div className="text-3xl font-bold text-primary mb-2">4</div>
            <div className="text-sm text-gray-600">Major Clients</div>
          </div>
          
          <div className="text-center p-6 bg-white rounded-xl shadow-sm">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-3xl font-bold text-green-600 mb-2">500+</div>
            <div className="text-sm text-gray-600">Projects Delivered</div>
          </div>
          
          <div className="text-center p-6 bg-white rounded-xl shadow-sm">
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Award className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="text-3xl font-bold text-yellow-600 mb-2">100%</div>
            <div className="text-sm text-gray-600">Client Satisfaction</div>
          </div>
          
          <div className="text-center p-6 bg-white rounded-xl shadow-sm">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
            <div className="text-3xl font-bold text-purple-600 mb-2">15+</div>
            <div className="text-sm text-gray-600">Years Partnership</div>
          </div>
        </div>

        {/* Major Clients */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Major Clients</h3>
          <div className="grid md:grid-cols-2 gap-8">
            {clients.map((client, index) => (
              <div key={index} className="bg-white rounded-xl p-8 shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">{client.name}</h4>
                    <p className="text-gray-600 mb-3">{client.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">{client.projects}</div>
                    <div className="text-xs text-gray-500">Projects</div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <span className="inline-flex items-center px-3 py-1 bg-primary/10 text-primary text-sm font-medium rounded-full">
                    {client.partnership}
                  </span>
                  <div className="flex items-center text-yellow-500">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials */}
        <div className="bg-white rounded-2xl p-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">What Our Clients Say</h3>
          <div className="grid md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="relative">
                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-500 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 mb-4 italic">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="text-sm">
                    <div className="font-semibold text-gray-900">{testimonial.author}</div>
                    <div className="text-gray-600">{testimonial.company}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Partnership CTA */}
        <div className="mt-16 text-center bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-12 text-white">
          <h3 className="text-3xl font-bold mb-4">Become Our Next Success Story</h3>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join our prestigious list of clients and experience the Elite Structure difference. 
            Let's build something extraordinary together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-primary hover:bg-gray-100 px-8 py-3 rounded-lg font-medium transition-colors">
              Start Partnership
            </button>
            <button className="border border-white text-primary bg-white hover:bg-primary hover:text-white px-8 py-3 rounded-lg font-medium transition-colors">
              Learn More
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Clients

