import { Phone, Mail, MapPin, Award, ExternalLink } from 'lucide-react'
import logo from '../assets/elite-removebg-preview.png'

const Footer = () => {
  const quickLinks = [
    { name: 'About Us', href: '#about' },
    { name: 'Services', href: '#services' },
    { name: 'Projects', href: '#projects' },
    { name: 'Clients', href: '#clients' },
    { name: 'Contact', href: '#contact' }
  ]

  const services = [
    'Oil & Gas Construction',
    'Refineries & Petrochemicals',
    'Power & Desalination',
    'Non-metallic Piping',
    'Scaffolding Services'
  ]

  const certifications = [
    'ISO 9001 Certified',
    'SAUDI ARAMCO Approved',
    'SABIC Certified',
    'Safety Compliant'
  ]

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-6">
              <img src={logo} alt="Elite Structure Company" className="h-16 w-auto" />
              <div className="ml-3">
                <h3 className="text-xl font-bold">Elite Structure</h3>
                <p className="text-gray-400">Company</p>
              </div>
            </div>
            <p className="text-gray-400 mb-6 text-sm leading-relaxed">
              Elite Structure Company is an ISO 9001 Certified Company with proven expertise 
              in the Saudi market since 2008. We are a premier sub-contractor for prestigious 
              companies including SAUDI ARAMCO, SABIC, and more.
            </p>
            <div className="flex items-center text-sm text-gray-400">
              <Award className="w-4 h-4 mr-2 text-accent" />
              ISO 9001 Certified Since 2008
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Our Services</h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index} className="text-gray-400 text-sm">
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Contact Information</h4>
            <div className="space-y-4">
              <div className="flex items-start">
                <MapPin className="w-4 h-4 mr-3 mt-1 text-primary flex-shrink-0" />
                <div className="text-sm text-gray-400">
                  <div className="font-medium text-white mb-1">Head Office</div>
                  <div>Jubail, Saudi Arabia</div>
                </div>
              </div>
              
              <div className="flex items-start">
                <Phone className="w-4 h-4 mr-3 mt-1 text-primary flex-shrink-0" />
                <div className="text-sm text-gray-400">
                  <div className="font-medium text-white mb-1">CEO</div>
                  <a href="tel:+966559789339" className="hover:text-white transition-colors">
                    +966559789339
                  </a>
                </div>
              </div>
              
              <div className="flex items-start">
                <Mail className="w-4 h-4 mr-3 mt-1 text-primary flex-shrink-0" />
                <div className="text-sm text-gray-400">
                  <div className="font-medium text-white mb-1">Email</div>
                  <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>

            {/* Certifications */}
            <div className="mt-8">
              <h5 className="text-sm font-semibold mb-3 text-gray-300">Certifications</h5>
              <div className="space-y-2">
                {certifications.map((cert, index) => (
                  <div key={index} className="flex items-center text-xs text-gray-400">
                    <div className="w-2 h-2 bg-accent rounded-full mr-2"></div>
                    {cert}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-400 mb-4 md:mb-0">
              © 2024 Elite Structure Company. All rights reserved.
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-white transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-white transition-colors">
                <span className="flex items-center">
                  Company Profile
                  <ExternalLink className="w-3 h-3 ml-1" />
                </span>
              </a>
            </div>
          </div>
          
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              Elite Structure Company - Building Excellence Since 2008 | 
              Premier Sub-Contractor for SAUDI ARAMCO, SABIC, SAUDI ELECTRICITY COMPANY, and MAADEN
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer

