import { useRef, useMemo } from 'react'
import { <PERSON>vas, useFrame } from '@react-three/fiber'
import { OrbitControls, Text, Box, Sphere, Cylinder } from '@react-three/drei'
import * as THREE from 'three'

// Construction crane component
function ConstructionCrane({ position = [0, 0, 0] }) {
  const craneRef = useRef()
  
  useFrame((state) => {
    if (craneRef.current) {
      craneRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={craneRef} position={position}>
      {/* Base */}
      <Box args={[1, 0.5, 1]} position={[0, -2, 0]}>
        <meshStandardMaterial color="#2563eb" />
      </Box>
      
      {/* Vertical mast */}
      <Cylinder args={[0.1, 0.1, 4]} position={[0, 0, 0]}>
        <meshStandardMaterial color="#1e40af" />
      </Cylinder>
      
      {/* Horizontal jib */}
      <Cylinder args={[0.05, 0.05, 3]} position={[1.5, 2, 0]} rotation={[0, 0, Math.PI / 2]}>
        <meshStandardMaterial color="#1e40af" />
      </Cylinder>
      
      {/* Counter jib */}
      <Cylinder args={[0.05, 0.05, 1.5]} position={[-0.75, 2, 0]} rotation={[0, 0, Math.PI / 2]}>
        <meshStandardMaterial color="#1e40af" />
      </Cylinder>
      
      {/* Hook */}
      <Sphere args={[0.1]} position={[2.5, 1.5, 0]}>
        <meshStandardMaterial color="#fbbf24" />
      </Sphere>
    </group>
  )
}

// Building structure component
function BuildingStructure({ position = [0, 0, 0] }) {
  const buildingRef = useRef()
  
  useFrame((state) => {
    if (buildingRef.current) {
      buildingRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.3) * 0.1
    }
  })

  return (
    <group ref={buildingRef} position={position}>
      {/* Foundation */}
      <Box args={[2, 0.3, 2]} position={[0, -1.5, 0]}>
        <meshStandardMaterial color="#6b7280" />
      </Box>
      
      {/* Building floors */}
      {[0, 1, 2].map((floor) => (
        <Box key={floor} args={[1.8, 0.8, 1.8]} position={[0, -1 + floor * 0.9, 0]}>
          <meshStandardMaterial color="#3b82f6" opacity={0.8} transparent />
        </Box>
      ))}
      
      {/* Structural beams */}
      {[-0.8, 0.8].map((x) => (
        <Cylinder key={x} args={[0.05, 0.05, 2.5]} position={[x, 0.25, 0]}>
          <meshStandardMaterial color="#1e40af" />
        </Cylinder>
      ))}
    </group>
  )
}

// Floating particles component
function FloatingParticles() {
  const particlesRef = useRef()
  
  const particles = useMemo(() => {
    const temp = []
    for (let i = 0; i < 50; i++) {
      temp.push({
        position: [
          (Math.random() - 0.5) * 10,
          (Math.random() - 0.5) * 10,
          (Math.random() - 0.5) * 10
        ],
        scale: Math.random() * 0.1 + 0.05
      })
    }
    return temp
  }, [])

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1
    }
  })

  return (
    <group ref={particlesRef}>
      {particles.map((particle, index) => (
        <Sphere key={index} args={[particle.scale]} position={particle.position}>
          <meshStandardMaterial color="#60a5fa" opacity={0.6} transparent />
        </Sphere>
      ))}
    </group>
  )
}

// Main Three.js scene component
function ThreeScene() {
  return (
    <div className="w-full h-full">
      <Canvas
        camera={{ position: [5, 3, 5], fov: 60 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} />
        
        {/* 3D Objects */}
        <ConstructionCrane position={[-2, 0, 0]} />
        <BuildingStructure position={[2, 0, 0]} />
        <FloatingParticles />
        
        {/* 3D Text */}
        <Text
          position={[0, 3, 0]}
          fontSize={0.5}
          color="#2563eb"
          anchorX="center"
          anchorY="middle"
          font="/fonts/Inter-Bold.woff"
        >
          Elite Structure
        </Text>
        
        {/* Controls */}
        <OrbitControls
          enableZoom={false}
          enablePan={false}
          autoRotate
          autoRotateSpeed={1}
          maxPolarAngle={Math.PI / 2}
          minPolarAngle={Math.PI / 4}
        />
      </Canvas>
    </div>
  )
}

export default ThreeScene

